#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件拆分工具
将Excel文件的每个sheet拆分为单独的CSV文件
"""

import pandas as pd
import os
import sys
from pathlib import Path

def process_column_a_empty_cells(df):
    """
    处理A列空单元格的逻辑：
    如果A列单元格为空，且B列有信息，则将A列赋值为上一行的值

    Args:
        df (DataFrame): 输入的数据框

    Returns:
        DataFrame: 处理后的数据框
    """
    if df.empty or len(df.columns) < 2:
        return df

    # 获取A列和B列的列名
    col_a = df.columns[0]  # A列
    col_b = df.columns[1]  # B列

    print(f"  处理A列空单元格逻辑 - A列: {col_a}, B列: {col_b}")

    # 遍历每一行（从第二行开始，因为第一行没有上一行）
    for i in range(1, len(df)):
        # 检查A列是否为空（NaN或空字符串）
        a_is_empty = pd.isna(df.iloc[i][col_a]) or str(df.iloc[i][col_a]).strip() == ''

        # 检查B列是否有信息（不为空且不是空字符串）
        b_has_info = not pd.isna(df.iloc[i][col_b]) and str(df.iloc[i][col_b]).strip() != ''

        # 如果A列为空且B列有信息，则将A列赋值为上一行的值
        if a_is_empty and b_has_info:
            prev_a_value = df.iloc[i-1][col_a]
            df.iloc[i, df.columns.get_loc(col_a)] = prev_a_value
            print(f"    第{i+1}行: A列空且B列有值，A列赋值为上一行的值: '{prev_a_value}'")

    return df

def standardize_column_a_values(df):
    """
    标准化A列的值，按照指定规则进行替换

    Args:
        df (DataFrame): 输入的数据框

    Returns:
        DataFrame: 处理后的数据框
    """
    if df.empty or len(df.columns) < 1:
        return df

    # 获取A列的列名
    col_a = df.columns[0]  # A列

    # 定义替换规则
    replacement_rules = {
        '合作情况': '【整体合作情况】',
        '合作感受（注意一级标题分段）': '【合作感受】',
        '【产品】（一级标题注意分段）': '【产品】',
        '【产品组合】（一级标题注意分段）': '【产品组合】',
        '未来合作意愿与投入': '【未来合作意愿与投入】',
        '未来期望和建议': '【未来期望和建议】'
    }

    print(f"  标准化A列值...")

    # 统计替换次数
    replacement_count = {}

    # 先检查前几行的值，用于调试
    print(f"  调试：前5行A列的值:")
    for i in range(min(5, len(df))):
        current_value = str(df.iloc[i][col_a])
        print(f"    第{i+1}行: '{current_value}' (类型: {type(df.iloc[i][col_a])})")

    # 遍历每一行进行替换
    for i in range(len(df)):
        current_value = str(df.iloc[i][col_a])

        # 检查是否需要替换
        if current_value in replacement_rules:
            new_value = replacement_rules[current_value]
            df.iloc[i, df.columns.get_loc(col_a)] = new_value

            # 统计替换次数
            if current_value not in replacement_count:
                replacement_count[current_value] = 0
            replacement_count[current_value] += 1

            print(f"    第{i+1}行: '{current_value}' -> '{new_value}'")

    # 输出替换统计
    for old_value, count in replacement_count.items():
        new_value = replacement_rules[old_value]
        print(f"    '{old_value}' -> '{new_value}' (共{count}次)")

    return df

def split_excel_to_csv(excel_file_path, output_dir=None):
    """
    将Excel文件的每个sheet拆分为单独的CSV文件
    
    Args:
        excel_file_path (str): Excel文件路径
        output_dir (str): 输出目录，默认为Excel文件所在目录
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(excel_file_path):
            print(f"错误：文件 {excel_file_path} 不存在")
            return False
        
        # 设置输出目录
        if output_dir is None:
            output_dir = os.path.dirname(excel_file_path)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"正在读取Excel文件: {excel_file_path}")
        
        # 读取Excel文件的所有sheet
        excel_file = pd.ExcelFile(excel_file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"发现 {len(sheet_names)} 个sheet: {sheet_names}")
        
        # 遍历每个sheet并保存为CSV
        for sheet_name in sheet_names:
            print(f"正在处理sheet: {sheet_name}")
            
            # 读取sheet数据
            df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

            # 处理A列空单元格的逻辑
            df = process_column_a_empty_cells(df)

            # 标准化A列的值
            df = standardize_column_a_values(df)

            # 清理sheet名称，移除不适合作为文件名的字符
            safe_sheet_name = "".join(c for c in sheet_name if c.isalnum() or c in (' ', '-', '_', '（', '）', '(', ')')).strip()
            
            # 构建输出文件路径
            csv_filename = f"{safe_sheet_name}.csv"
            csv_filepath = os.path.join(output_dir, csv_filename)
            
            # 保存为CSV文件
            df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
            
            print(f"已保存: {csv_filepath} (共 {len(df)} 行数据)")
        
        print(f"\n拆分完成！共处理了 {len(sheet_names)} 个sheet")
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

def process_single_sheet_demo(excel_file_path, sheet_name, output_dir=None):
    """
    处理单个sheet的演示函数
    """
    try:
        if output_dir is None:
            output_dir = os.path.dirname(excel_file_path)

        print(f"正在演示处理sheet: {sheet_name}")

        # 读取指定sheet数据
        df_original = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        print(f"原始数据前10行:")
        print(df_original.head(10))
        print("\n" + "="*50)

        # 处理A列空单元格的逻辑
        df_processed = process_column_a_empty_cells(df_original.copy())

        print(f"\n处理后数据前10行:")
        print(df_processed.head(10))

        # 保存处理后的文件
        safe_sheet_name = "".join(c for c in sheet_name if c.isalnum() or c in (' ', '-', '_', '（', '）', '(', ')')).strip()
        csv_filename = f"{safe_sheet_name}_processed_demo.csv"
        csv_filepath = os.path.join(output_dir, csv_filename)

        df_processed.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
        print(f"\n已保存演示文件: {csv_filepath}")

        return True

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

def main():
    # Excel文件路径 - 使用完整路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_file_path = os.path.join(current_dir, "纪要标签-合集-0210.xlsx")

    print("=" * 50)
    print("Excel文件拆分工具 - 单sheet演示")
    print("=" * 50)

    # 演示处理单个sheet
    demo_sheet = "企业NA客户"  # 选择一个sheet进行演示
    success = process_single_sheet_demo(excel_file_path, demo_sheet)

    if success:
        print("\n✅ 演示处理成功完成！")
    else:
        print("\n❌ 演示处理过程中出现错误")
        sys.exit(1)

if __name__ == "__main__":
    main()
