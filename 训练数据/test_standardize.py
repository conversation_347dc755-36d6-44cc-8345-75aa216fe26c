#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标准化功能
"""

import pandas as pd
import os

def standardize_column_a_values(df):
    """
    标准化A列的值，按照指定规则进行替换
    """
    if df.empty or len(df.columns) < 1:
        return df
    
    # 获取A列的列名
    col_a = df.columns[0]  # A列
    
    # 定义替换规则
    replacement_rules = {
        '合作情况': '【整体合作情况】',
        '合作感受（注意一级标题分段）': '【合作感受】',
        '【产品】（一级标题注意分段）': '【产品】',
        '【产品组合】（一级标题注意分段）': '【产品组合】',
        '未来合作意愿与投入': '【未来合作意愿与投入】',
        '未来期望和建议': '【未来期望和建议】'
    }
    
    print(f"标准化A列值...")
    print(f"A列名称: {col_a}")
    
    # 统计替换次数
    replacement_count = {}
    
    # 先检查前几行的值，用于调试
    print(f"调试：前10行A列的值:")
    for i in range(min(10, len(df))):
        current_value = str(df.iloc[i][col_a])
        print(f"  第{i+1}行: '{current_value}' (类型: {type(df.iloc[i][col_a])})")
    
    # 遍历每一行进行替换
    for i in range(len(df)):
        current_value = str(df.iloc[i][col_a])
        
        # 检查是否需要替换
        if current_value in replacement_rules:
            new_value = replacement_rules[current_value]
            df.iloc[i, df.columns.get_loc(col_a)] = new_value
            
            # 统计替换次数
            if current_value not in replacement_count:
                replacement_count[current_value] = 0
            replacement_count[current_value] += 1
            
            print(f"  第{i+1}行: '{current_value}' -> '{new_value}'")
    
    # 输出替换统计
    if replacement_count:
        print(f"替换统计:")
        for old_value, count in replacement_count.items():
            new_value = replacement_rules[old_value]
            print(f"  '{old_value}' -> '{new_value}' (共{count}次)")
    else:
        print("没有找到需要替换的值")
    
    return df

def main():
    # 读取已处理的文件
    csv_file = "企业NA客户_processed_demo.csv"
    
    print("=" * 50)
    print("测试标准化功能")
    print("=" * 50)
    
    # 读取CSV文件
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    print(f"读取文件: {csv_file}")
    print(f"数据形状: {df.shape}")
    
    # 执行标准化
    df_standardized = standardize_column_a_values(df)
    
    # 保存标准化后的文件
    output_file = "企业NA客户_standardized_test.csv"
    df_standardized.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n已保存标准化后的文件: {output_file}")

if __name__ == "__main__":
    main()
